@import 'variables';

// Override Aura theme colors with your brand colors
:root {
  // Primary colors based on your variables
  --p-primary-50: #f0f0ff;
  --p-primary-100: #e0e0ff;
  --p-primary-200: #c7c7ff;
  --p-primary-300: #a5a5ff;
  --p-primary-400: #8080ff;
  --p-primary-500: #{$primary-blue-01}; // #1E0E70 - Your main brand color
  --p-primary-600: #1a0c63;
  --p-primary-700: #160a56;
  --p-primary-800: #120849;
  --p-primary-900: #0e063c;
  --p-primary-950: #0a042f;
}

// Force ProgressSpinner to use only your single brand color
.p-progressspinner .p-progress-spinner-circle {
  stroke: #{$primary-blue-01} !important;
  fill: none !important;
}

// Override any gradient or multi-color effects
.p-progressspinner svg circle {
  stroke: #{$primary-blue-01} !important;
  fill: transparent !important;
}

// Additional selectors to ensure single color
p-progressspinner .p-progress-spinner-circle,
p-progressspinner svg circle {
  stroke: #{$primary-blue-01} !important;
  fill: transparent !important;
}

// Global override for any ProgressSpinner instance
::ng-deep .p-progressspinner .p-progress-spinner-circle,
::ng-deep .p-progressspinner svg circle {
  stroke: #{$primary-blue-01} !important;
  fill: transparent !important;
}

// Button styling to match your primary button
.p-button {
  background-color: #{$primary-blue-01};
  border-color: #{$primary-blue-01};
  color: #ffffff;
  border-radius: 7px;
  font-family: "SoleSans-SemiBold", sans-serif;

  &:hover {
    background-color: #1a0c63;
    border-color: #1a0c63;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(30, 14, 112, 0.25);
  }
}

// Input styling to match your design - clean outline style
.p-inputtext {
  background-color: transparent !important;
  border: 1px solid #{$primary-grey} !important;
  border-radius: 10px !important;
  padding: 12px 24px !important;
  height: 50px !important;
  color: #333 !important;

  &:enabled:focus {
    outline: 0 none !important;
    outline-offset: 0 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 14, 112, 0.25) !important;
    border-color: #{$primary-blue-01} !important;
    background-color: transparent !important;
  }

  &:enabled:hover {
    border-color: #{$primary-blue-01} !important;
    background-color: transparent !important;
  }
}

// Password component styling
.p-password {
  .p-inputtext {
    background-color: transparent !important;
    border: 1px solid #{$primary-grey} !important;
    border-radius: 10px !important;
    padding: 12px 24px !important;
    height: 50px !important;
    width: 100% !important;

    &:enabled:focus {
      outline: 0 none !important;
      outline-offset: 0 !important;
      box-shadow: 0 0 0 0.2rem rgba(30, 14, 112, 0.25) !important;
      border-color: #{$primary-blue-01} !important;
      background-color: transparent !important;
    }

    &:enabled:hover {
      border-color: #{$primary-blue-01} !important;
      background-color: transparent !important;
    }
  }

  .p-password-toggle-mask {
    color: #{$primary-grey} !important;

    &:hover {
      color: #{$primary-blue-01} !important;
    }
  }
}

// Card styling
.p-card {
  background: #ffffff !important;
  border: 1px solid #e9ecef !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;

  .p-card-header {
    background: transparent !important;
    border: none !important; // Remove all borders
    border-bottom: none !important; // Remove divider
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    padding: 1.5rem 1.5rem 0 1.5rem !important; // Remove bottom padding
    box-shadow: none !important; // Remove any shadow
    outline: none !important;
  }

  .p-card-body {
    padding: 1rem 1.5rem 1.5rem 1.5rem !important; // Reduce top padding
  }
}

// Message styling
.p-message {
  border-radius: 8px !important;

  &.p-message-error {
    background-color: #fef2f2 !important;
    border: 1px solid #fecaca !important;
    color: #dc2626 !important;
  }
}

// Label styling
.p-label {
  color: #{$primary-blue-01};
  font-size: 15px;
  font-family: "SoleSans-SemiBold", sans-serif;
}
