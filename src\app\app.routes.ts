import { Routes } from '@angular/router';
import { isSignedInGuard } from './is-signed-in.guard';
import { isLoginGuard } from './is-login.guard';

export const routes: Routes = [
  { 
    path: 'login', 
    loadComponent: () => import('./pages/login/login.component').then(m => m.LoginComponent),
    canActivate: [isLoginGuard] 
  },
  { 
    path: 'home', 
    loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent),
    canActivate: [isSignedInGuard] 
  },
  { path: '', redirectTo: '/login', pathMatch: 'full' },
  { path: '**', redirectTo: '/login' }
];
