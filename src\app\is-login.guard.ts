import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from './services/auth/auth.service';
import { map, catchError, of } from 'rxjs';

export const isLoginGuard = () => {
  const router = inject(Router);
  const authService = inject(AuthService);

  const [isLoggedIn] = authService.isLoggedIn(true);

  if (!isLoggedIn) {
    return true;
  }

  return authService.isAuthenticated().pipe(
    map(isAuthenticated => {
      if (isAuthenticated) {
        router.navigate(['/home']);
        return false;
      }
      return true;
    }),
    catchError(() => of(true))
  );
};
