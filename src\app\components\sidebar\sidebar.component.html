<div class="sidebar" [ngClass]="{ 'collapsed': sidebarCollapsed }">
    <div class="button-container" [ngClass]="{ 'button-container-collapsed': sidebarCollapsed}">
        @for (route of routes; track $index) {
        <div *ngIf="!!route.visible" class="route-button"
            [ngClass]="{ 'route-button-active': router.url == route.path , 'route-button-collapsed': sidebarCollapsed }"
            [routerLink]="route.path">
            <div class="icon-box" [ngClass]="{ 'icon-box-collapsed': sidebarCollapsed}">
                <i class="icon" [ngClass]="route.icon"></i>
            </div>
            <p class="ui-p-light" [ngClass]="{ 'hide': sidebarCollapsed, 'text-white': router.url == route.path }">
                {{ route.text }}
            </p>
        </div>
        }
    </div>
</div>

<!-- Toggle button in overlay -->
<p-button class="sidebar-toggle-btn" (click)="toggleSidebar()" [text]="true" severity="secondary">
    a
</p-button>