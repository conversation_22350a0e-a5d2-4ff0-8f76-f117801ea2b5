import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';

@Component({
  selector: 'app-sidebar',
  imports: [RouterModule, CommonModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
})
export class SidebarComponent {
  sidebarCollapsed = false;
  isOpen = true;

  routes = [
    {
      icon: 'i-document',
      path: '/reports',
      text: 'Reports',
      visible: true,
    },
    {
      icon: 'i-paper-clip',
      path: '/integrations',
      text: 'Integrations',
      visible: true,
    },
  ];

  constructor(public router: Router) {}

  closeSidebar() {
    this.sidebarCollapsed = !this.sidebarCollapsed;
  }
}
