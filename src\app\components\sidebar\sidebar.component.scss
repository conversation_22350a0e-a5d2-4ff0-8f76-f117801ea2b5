@import "../../../assets/styles/_variables";

.sidebar {
  width: 316px;
  height: 100vh;
  background-color: $neutral-light-grey;
  display: flex;
  padding: 80px 24px 24px;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  overflow: auto;
}

.button-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.route-button {
  width: 100%;
  padding: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 12px;
  border-radius: 8px;
}

.route-button:hover {
  background-color: $primary-blue-03;
}

.route-button-active {
  background-color: $primary-blue-02;
  border-radius: 8px;
}

.icon-box {
  width: 40px;
  height: 40px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
  margin-right: 24px;
}

.i-paper-clip, .i-document {
  width: 24px;
  height: 24px;
  min-height: 24px; /* Altezza minima */
  min-width: 24px; /* Altezza minima */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 13px;
  margin-right: 13px;
}
